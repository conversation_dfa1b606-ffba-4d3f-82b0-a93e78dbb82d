<?php

namespace App\Http\Controllers;

use App\Constants\PaymentConstants;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\OrderReference;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Webhook;
use WebToPay;
use Illuminate\Support\Str;
use Stripe\Stripe;

class PaymentController extends Controller
{
    private $projectId;
    private $signPassword;
    private $esimAccessController;

    public function __construct(ESimAccessController $esimAccessController)
    {
        $this->projectId = config('services.paysera.project_id');
        $this->signPassword = config('services.paysera.sign_password');
        $this->esimAccessController = $esimAccessController;
    }

    public function initiate(Order $order)
    {
        $reference = $this->createOrderReference($order, PaymentConstants::GATEWAY_PAYSERA);

        return $this->createPayseraPayment($order, $reference);
    }

    protected function createPayseraPayment(Order $order, OrderReference $reference)
    {
        $request = [
            'projectid'     => $this->projectId,
            'sign_password' => $this->signPassword,
            'orderid'       => $reference->reference_id,
            'amount'        => $order->amount * 100, // Paysera expects amount in cents
            'currency'      => $order->currency,
            'country'       => PaymentConstants::COUNTRY_LITHUANIA,
            'accepturl'     => route('payment.success'),
            'cancelurl'     => route('payment.cancel'),
            'callbackurl'   => route('payment.callback'),
            'test'          => config('app.env') !== 'production',
            'p_email'       => $order->email,
            'p_firstname'   => $order->email,
            'p_lastname'    => $order->email,
        ];

        return WebToPay::redirectToPayment($request);
    }

    public function success(Request $request)
    {
        try {
            $response = WebToPay::validateAndParseData($request->all(), $this->projectId, $this->signPassword);

            $reference = OrderReference::where('reference_id', $response['orderid'])->firstOrFail();
            $order = $reference->order;

            $this->markPaymentAsSuccessful($order, $reference);

            return redirect()->route('payment.success.page');
        } catch (\Exception $e) {
            return redirect()->route('payment.error.page');
        }
    }

    public function stripeHandle(Request $request)
    {
       return (new StripeService)->handleWebhook($request);
    }

    public function cancel(Request $request)
    {
        try {
            $response = WebToPay::validateAndParseData($request->all(), $this->projectId, $this->signPassword);

            $reference = OrderReference::where('reference_id', $response['orderid'])->firstOrFail();
            $order = $reference->order;

            $order->update(['status' => PaymentConstants::STATUS_CANCELLED]);
            $reference->update(['status' => PaymentConstants::STATUS_CANCELLED]);

            return redirect()->route('payment.cancel.page');
        } catch (\Exception $e) {
            return redirect()->route('payment.error.page');
        }
    }

    public function callback(Request $request)
    {
        try {
            $response = WebToPay::validateAndParseData($request->all(), $this->projectId, $this->signPassword);

            $reference = OrderReference::where('reference_id', $response['orderid'])->firstOrFail();
            $order = $reference->order;

            if ($this->isPaymentSuccessful($response['status'])) {
                $this->esimAccessController->createESimOrder($order);

                $this->markPaymentAsSuccessful($order, $reference);

                if ($order->coupon_code) {
                    $this->markCouponAsUsed($order);
                }
            } else {
                $this->markPaymentAsFailed($order, $reference);
            }

            return response()->json(['status' => 'OK']);
        } catch (\Exception $e) {
            Log::error('Payment callback error', [
                'message' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json(['status' => 'ERROR', 'message' => $e->getMessage()], 400);
        }
    }

    /**
     * Kupon varsa ve daha önce sayılmadıysa kullanım sayısını artır
     *
     * @param $order
     * @return void
     */
    public function markCouponAsUsed($order): void
    {
        $coupon = Coupon::where('code', $order->coupon_code)->first();

        if ($coupon && $coupon->isUsable()) {
            $coupon->increment('used');
        }
    }

    /**
     * @param $order
     * @param $reference
     * @return void
     */
    public function markPaymentAsFailed($order, $reference): void
    {
        $order->update(['status' => PaymentConstants::STATUS_FAILED]);
        $reference->update(['status' => PaymentConstants::STATUS_FAILED]);
    }

    /**
     * @param Order $order
     * @param OrderReference $reference
     * @return void
     */
    public function markPaymentAsSuccessful(Order $order, OrderReference $reference): void
    {
        $order->update(['status' => PaymentConstants::STATUS_PAID]);
        $reference->update(['status' => PaymentConstants::STATUS_COMPLETED]);
    }

    /**
     *
     * @TODO: === strict type can be create problem
     *
     * @param $status
     * @return bool
     */
    public function isPaymentSuccessful($status): bool
    {
        return $status === PaymentConstants::PAYSERA_SUCCESS_STATUS;
    }

    /**
     * @param Order $order
     * @param string $gateway
     * @return mixed
     */
    public function createOrderReference(Order $order, string $gateway)
    {
        $reference = OrderReference::create([
            'order_id' => $order->id,
            'gateway' => $gateway,
            'reference_id' => PaymentConstants::REFERENCE_PREFIX . Str::uuid(),
            'status' => PaymentConstants::STATUS_INITIATED,
        ]);
        return $reference;
    }
}
