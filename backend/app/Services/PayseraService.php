<?php

namespace App\Services;

use App\Constants\PaymentConstants;
use App\Contracts\PaymentServiceInterface;
use App\Http\Controllers\ESimAccessController;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\OrderReference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use WebToPay;

class PayseraService implements PaymentServiceInterface
{
    private $projectId;
    private $signPassword;
    private $esimAccessController;

    public function __construct(ESimAccessController $esimAccessController)
    {
        $this->projectId = config('services.paysera.project_id');
        $this->signPassword = config('services.paysera.sign_password');
        $this->esimAccessController = $esimAccessController;
    }

    /**
     * Create a payment for the given order
     *
     * @param Order $order
     * @return mixed
     */
    public function createPayment(Order $order)
    {
        $reference = $this->createOrderReference($order);

        return $this->createPayseraPayment($order, $reference);
    }

    /**
     * Handle webhook/callback from payment gateway
     *
     * @param Request $request
     * @return mixed
     */
    public function handleWebhook(Request $request)
    {
        try {
            $response = WebToPay::validateAndParseData($request->all(), $this->projectId, $this->signPassword);

            $reference = OrderReference::where('reference_id', $response['orderid'])->firstOrFail();
            $order = $reference->order;

            if ($this->isPaymentSuccessful($response['status'])) {
                $this->esimAccessController->createESimOrder($order);

                $this->markPaymentAsSuccessful($order, $reference);

                if ($order->coupon_code) {
                    $this->markCouponAsUsed($order);
                }
            } else {
                $this->markPaymentAsFailed($order, $reference);
            }

            return response()->json(['status' => 'OK']);
        } catch (\Exception $e) {
            Log::error('Paysera callback error', [
                'message' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json(['status' => 'ERROR', 'message' => $e->getMessage()], 400);
        }
    }

    /**
     * Get the gateway name for this payment service
     *
     * @return string
     */
    public function getGatewayName(): string
    {
        return PaymentConstants::GATEWAY_PAYSERA;
    }

    /**
     * Mark payment as successful
     *
     * @param Order $order
     * @param OrderReference $reference
     * @return void
     */
    public function markPaymentAsSuccessful(Order $order, OrderReference $reference): void
    {
        $order->update(['status' => PaymentConstants::STATUS_PAID]);
        $reference->update(['status' => PaymentConstants::STATUS_COMPLETED]);
    }

    /**
     * Mark payment as failed
     *
     * @param Order $order
     * @param OrderReference $reference
     * @return void
     */
    public function markPaymentAsFailed(Order $order, OrderReference $reference): void
    {
        $order->update(['status' => PaymentConstants::STATUS_FAILED]);
        $reference->update(['status' => PaymentConstants::STATUS_FAILED]);
    }

    /**
     * Create order reference for tracking
     *
     * @param Order $order
     * @param string|null $referenceId
     * @return OrderReference
     */
    public function createOrderReference(Order $order, string $referenceId = null): OrderReference
    {
        $reference = OrderReference::create([
            'order_id' => $order->id,
            'gateway' => $this->getGatewayName(),
            'reference_id' => $referenceId ?: PaymentConstants::REFERENCE_PREFIX . Str::uuid(),
            'status' => PaymentConstants::STATUS_INITIATED,
        ]);
        return $reference;
    }

    /**
     * Create Paysera payment request
     *
     * @param Order $order
     * @param OrderReference $reference
     * @return mixed
     */
    protected function createPayseraPayment(Order $order, OrderReference $reference)
    {
        $request = [
            'projectid'     => $this->projectId,
            'sign_password' => $this->signPassword,
            'orderid'       => $reference->reference_id,
            'amount'        => $order->amount * 100, // Paysera expects amount in cents
            'currency'      => $order->currency,
            'country'       => PaymentConstants::COUNTRY_LITHUANIA,
            'accepturl'     => route('payment.success'),
            'cancelurl'     => route('payment.cancel'),
            'callbackurl'   => route('payment.callback'),
            'test'          => config('app.env') !== 'production',
            'p_email'       => $order->email,
            'p_firstname'   => $order->email,
            'p_lastname'    => $order->email,
        ];

        return WebToPay::redirectToPayment($request);
    }

    /**
     * Check if payment is successful
     *
     * @param $status
     * @return bool
     */
    protected function isPaymentSuccessful($status): bool
    {
        return $status === PaymentConstants::PAYSERA_SUCCESS_STATUS;
    }

    /**
     * Mark coupon as used if applicable
     *
     * @param Order $order
     * @return void
     */
    protected function markCouponAsUsed(Order $order): void
    {
        $coupon = Coupon::where('code', $order->coupon_code)->first();

        if ($coupon && $coupon->isUsable()) {
            $coupon->increment('used');
        }
    }

    /**
     * Handle success callback
     *
     * @param Request $request
     * @return mixed
     */
    public function handleSuccess(Request $request)
    {
        try {
            $response = WebToPay::validateAndParseData($request->all(), $this->projectId, $this->signPassword);

            $reference = OrderReference::where('reference_id', $response['orderid'])->firstOrFail();
            $order = $reference->order;

            $this->markPaymentAsSuccessful($order, $reference);

            return redirect()->route('payment.success.page');
        } catch (\Exception $e) {
            return redirect()->route('payment.error.page');
        }
    }

    /**
     * Handle cancel callback
     *
     * @param Request $request
     * @return mixed
     */
    public function handleCancel(Request $request)
    {
        try {
            $response = WebToPay::validateAndParseData($request->all(), $this->projectId, $this->signPassword);

            $reference = OrderReference::where('reference_id', $response['orderid'])->firstOrFail();
            $order = $reference->order;

            $order->update(['status' => PaymentConstants::STATUS_CANCELLED]);
            $reference->update(['status' => PaymentConstants::STATUS_CANCELLED]);

            return redirect()->route('payment.cancel.page');
        } catch (\Exception $e) {
            return redirect()->route('payment.error.page');
        }
    }
}
